<?php

namespace Modules\Menu\Http\Controllers\Web;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreMenuRequest;
use Modules\Menu\Http\Requests\UpdateMenuRequest;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;
use Yajra\DataTables\Facades\DataTables;

class MenuController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Display menus datatable
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $branchId = BranchHelper::getCurrentBranchId();
            $menus = $this->menuService->getMenusForDataTable($branchId);

            // Apply filters
            if ($request->has('status') && $request->status !== '') {
                $status = $request->status === 'active' ? 1 : 0;
                $menus = $menus->where('is_active', $status);
            }

            if ($request->has('menu_type') && $request->menu_type !== '') {
                $menus = $menus->where('menu_type', $request->menu_type);
            }

            if ($request->has('is_default') && $request->is_default !== '') {
                $default = $request->is_default === 'yes' ? 1 : 0;
                $menus = $menus->where('is_default', $default);
            }

            return DataTables::of($menus)
                ->addIndexColumn()
                ->addColumn('action', function($row) {
                    $btn = '<div class="flex space-x-2">';
                    $btn .= '<button type="button" class="text-blue-600 hover:text-blue-900 show-menu" data-id="'.$row->id.'" title="عرض"><i class="fas fa-eye"></i></button>';
                    $btn .= '<button type="button" class="text-yellow-600 hover:text-yellow-900 edit-menu" data-id="'.$row->id.'" title="تعديل"><i class="fas fa-edit"></i></button>';
                    $btn .= '<button type="button" class="text-red-600 hover:text-red-900 delete-menu" data-id="'.$row->id.'" title="حذف"><i class="fas fa-trash"></i></button>';
                    $btn .= '</div>';
                    return $btn;
                })
                ->editColumn('is_active', function($row) {
                    return $row->is_active ? '<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">نشط</span>' : '<span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">غير نشط</span>';
                })
                ->addColumn('is_default', function($row) {
                    return $row->is_default ? '<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">افتراضي</span>' : '<span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">عادي</span>';
                })
                ->editColumn('menu_type', function($row) {
                    return $row->menu_type ? '<span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">' . $row->menu_type . '</span>' : '-';
                })
                ->editColumn('start_time', function($row) {
                    return $row->start_time ? $row->start_time : '-';
                })
                ->editColumn('end_time', function($row) {
                    return $row->end_time ? $row->end_time : '-';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at ? $row->created_at->format('Y-m-d H:i') : '-';
                })
                ->rawColumns(['action', 'is_active', 'is_default', 'menu_type'])
                ->make(true);
        }

        return view('menu::menus');
    }

    /**
     * Store a newly created menu
     */
    public function store(StoreMenuRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $menu = $this->menuService->createMenuForWeb($data);

            return ResponseHelper::success($menu, 'تم إضافة القائمة بنجاح');
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في إضافة القائمة: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified menu
     */
    public function show($id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menu = $this->menuService->getMenuByIdForBranch($id, $branchId);

            if (!$menu) {
                return ResponseHelper::notFound('القائمة غير موجودة');
            }

            return ResponseHelper::success($menu);
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في استرجاع القائمة: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified menu
     */
    public function edit($id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menu = $this->menuService->getMenuByIdForBranch($id, $branchId);

            if (!$menu) {
                return ResponseHelper::notFound('القائمة غير موجودة');
            }

            return ResponseHelper::success($menu);
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في استرجاع القائمة للتعديل: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified menu
     */
    public function update(UpdateMenuRequest $request, $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            
            $menu = $this->menuService->updateMenuForWeb($id, $data, $branchId);

            if (!$menu) {
                return ResponseHelper::notFound('القائمة غير موجودة');
            }

            return ResponseHelper::success($menu, 'تم تحديث القائمة بنجاح');
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في تحديث القائمة: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified menu
     */
    public function destroy($id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->menuService->deleteMenuForBranch($id, $branchId);

            if (!$deleted) {
                return ResponseHelper::notFound('القائمة غير موجودة');
            }

            return ResponseHelper::success(null, 'تم حذف القائمة بنجاح');
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في حذف القائمة: ' . $e->getMessage());
        }
    }

    /**
     * Get menus list for dropdowns
     */
    public function getMenusList(): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menus = $this->menuService->getMenusListForBranch($branchId);

            return ResponseHelper::success($menus);
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في استرجاع قائمة القوائم: ' . $e->getMessage());
        }
    }
}