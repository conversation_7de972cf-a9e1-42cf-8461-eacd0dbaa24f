<?php

namespace Modules\Orders\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\MenuItem;
use App\Models\Table;
use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Orders\Http\Requests\StoreOrderRequest;
use Modules\Orders\Services\OrderService;
use Modules\Kitchen\Services\KitchenService;

class POSController extends Controller
{
    protected $orderService;
    protected $kitchenService;

    public function __construct(OrderService $orderService, KitchenService $kitchenService)
    {
        $this->orderService = $orderService;
        $this->kitchenService = $kitchenService;
    }

    /**
     * Display POS dashboard
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get recent orders for the current branch
        $recentOrders = Order::where('branch_id', $user->branch_id)
            ->with(['customer', 'table', 'orderItems'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('orders::pos.index', compact('recentOrders'));
    }

    /**
     * Show the order creation form
     */
    public function create()
    {
        $user = Auth::user();
        
        // Get customers for the current branch/tenant
        $customers = Customer::where('tenant_id', $user->tenant_id)
            ->where('is_active', true)
            ->orderBy('first_name')
            ->get();

        // Get available tables for the current branch
        $tables = Table::where('branch_id', $user->branch_id)
            ->where('is_active', true)
            ->orderBy('table_number')
            ->get();

        // Get delivery personnel (users with delivery role)
        $deliveryPersonnel = User::where('tenant_id', $user->tenant_id)
            ->where('is_active', true)
            ->whereHas('roles', function($query) {
                $query->where('name', 'delivery');
            })
            ->orderBy('name')
            ->get();

        // Get menu items for the current branch
        $menuItems = MenuItem::whereHas('menu', function($query) use ($user) {
                $query->where('branch_id', $user->branch_id)
                      ->where('is_active', true);
            })
            ->with(['category', 'variants', 'addons', 'menu'])
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // If no menu items found, try to get all active menu items (fallback)
        if ($menuItems->isEmpty()) {
            $menuItems = MenuItem::with(['category', 'variants', 'addons', 'menu'])
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->limit(20) // Limit for demo purposes
                ->get();
        }

        // Group menu items by category name, handling null categories
        $menuCategories = $menuItems->groupBy(function($item) {
            return $item->category ? $item->category->name : 'Uncategorized';
        });

        return view('orders::pos.create', compact(
            'customers', 
            'tables', 
            'deliveryPersonnel', 
            'menuItems',
            'menuCategories'
        ));
    }

    /**
     * Store a new order from POS (Web form submission)
     */
    public function store(StoreOrderRequest $request)
    {
        try {
            \Log::info('POS Order Request:', $request->validated());
            $user = Auth::user();
            
            // Add branch and tenant info
            $data = $request->validated();
            $data['branch_id'] = $user->branch_id;
            $data['tenant_id'] = $user->tenant_id;
            $data['cashier_id'] = $user->id;
            
            // Ensure order status is confirmed for KOT creation
            if (!isset($data['status']) || $data['status'] !== 'cancelled') {
                $data['status'] = 'confirmed';
            }

            // Create the order (KOT creation is handled automatically in OrderService)
            $order = $this->orderService->createOrder($data);

            // Check if KOT was created (for response message)
            $kotCreated = $order->hasActiveKot();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Order created successfully' . ($kotCreated ? ' with KOT orders' : ''),
                    'data' => $order,
                    'kot_created' => $kotCreated,
                    'redirect' => route('pos.orders.kot', $order)
                ]);
            }

            return redirect()->route('pos.orders.kot', $order)->with('success', 'Order created successfully');

        } catch (\Exception $e) {
            \Log::error('POS Order creation failed: ' . $e->getMessage(), [
                'request_data' => $request->validated(),
                'trace' => $e->getTraceAsString()
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create order: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Failed to create order: ' . $e->getMessage());
        }
    }

    /**
     * Generate and display KOT
     */
    public function generateKOT(Order $order)
    {
        $user = Auth::user();
        
        // Verify order belongs to user's branch
        if ($order->branch_id !== $user->branch_id) {
            abort(404);
        }

        $order->load(['orderItems.menuItem', 'customer', 'table', 'branch']);

        return view('orders::pos.kot', compact('order'));
    }

    /**
     * Print KOT
     */
    public function printKOT(Order $order)
    {
        $user = Auth::user();
        
        // Verify order belongs to user's branch
        if ($order->branch_id !== $user->branch_id) {
            abort(404);
        }

        $order->load(['orderItems.menuItem', 'customer', 'table', 'branch']);

        return view('orders::pos.kot-print', compact('order'));
    }

    /**
     * Get menu item variants and addons for AJAX requests (Web)
     */
    public function getMenuItemAddons(MenuItem $menuItem)
    {
        $user = Auth::user();

        // Verify menu item belongs to user's branch
        if (!$menuItem->menu || $menuItem->menu->branch_id !== $user->branch_id) {
            return response()->json(['error' => 'Menu item not found'], 404);
        }

        $menuItem->load(['variants', 'addons']);

        return response()->json([
            'variants' => $menuItem->variants,
            'addons' => $menuItem->addons
        ]);
    }

    /**
     * Get menu item details with variants and addons (AJAX for Web)
     */
    public function getMenuItemDetails(MenuItem $menuItem)
    {
        $user = Auth::user();

        // Verify menu item belongs to user's branch
        if (!$menuItem->menu || $menuItem->menu->branch_id !== $user->branch_id) {
            return response()->json(['error' => 'Menu item not found'], 404);
        }

        $menuItem->load(['variants' => function($query) {
            $query->where('is_active', true)->orderBy('sort_order');
        }, 'addons' => function($query) {
            $query->orderBy('addon_group_name')->orderBy('sort_order');
        }]);

        return response()->json([
            'id' => $menuItem->id,
            'name' => $menuItem->name,
            'description' => $menuItem->description,
            'base_price' => $menuItem->base_price,
            'image' => $menuItem->image,
            'variants' => $menuItem->variants->map(function($variant) {
                return [
                    'id' => $variant->id,
                    'name' => $variant->name,
                    'price_modifier' => $variant->price_modifier,
                    'is_default' => $variant->is_default,
                ];
            }),
            'addons' => $menuItem->addons->groupBy('addon_group_name')->map(function($group, $groupName) {
                return [
                    'group_name' => $groupName,
                    'items' => $group->map(function($addon) {
                        return [
                            'id' => $addon->id,
                            'name' => $addon->name,
                            'price' => $addon->price,
                            'is_required' => $addon->is_required,
                            'max_quantity' => $addon->max_quantity,
                        ];
                    })
                ];
            })->values()
        ]);
    }

    /**
     * Calculate order totals for AJAX requests (Web)
     */
    public function calculateOrderTotals(Request $request)
    {
        $items = $request->input('items', []);
        $orderDiscount = $request->input('order_discount', []);
        $taxRate = $request->input('tax_rate', 0.1); // Default 10% tax

        $subtotal = 0;
        $totalDiscount = 0;

        foreach ($items as $item) {
            $itemSubtotal = ($item['unit_price'] ?? 0) * ($item['quantity'] ?? 1);
            
            // Add addon costs
            if (isset($item['addons']) && is_array($item['addons'])) {
                foreach ($item['addons'] as $addon) {
                    $itemSubtotal += ($addon['unit_price'] ?? 0) * ($addon['quantity'] ?? 1);
                }
            }

            $subtotal += $itemSubtotal;
        }

        // Apply order-level discount
        if (!empty($orderDiscount)) {
            if ($orderDiscount['type'] === 'percentage') {
                $orderDiscountAmount = $subtotal * ($orderDiscount['value'] / 100);
            } else {
                $orderDiscountAmount = $orderDiscount['value'];
            }
            $subtotal -= $orderDiscountAmount;
            $totalDiscount += $orderDiscountAmount;
        }

        $taxAmount = $subtotal * $taxRate;
        $total = $subtotal + $taxAmount;

        return response()->json([
            'subtotal' => round($subtotal, 2),
            'tax_amount' => round($taxAmount, 2),
            'total_discount' => round($totalDiscount, 2),
            'total' => round($total, 2)
        ]);
    }

    /**
     * Get service worker manifest for POS
     */
    public function getServiceWorkerManifest()
    {
        $manifest = [
            'name' => 'Restaurant POS System',
            'short_name' => 'POS',
            'description' => 'Offline-capable Point of Sale System',
            'start_url' => '/pos/create',
            'display' => 'standalone',
            'background_color' => '#ffffff',
            'theme_color' => '#4f46e5',
            'orientation' => 'portrait',
            'icons' => [
                [
                    'src' => '/assets/images/pos-icon-192.png',
                    'sizes' => '192x192',
                    'type' => 'image/png'
                ],
                [
                    'src' => '/assets/images/pos-icon-512.png',
                    'sizes' => '512x512',
                    'type' => 'image/png'
                ]
            ]
        ];

        return response()->json($manifest)
            ->header('Content-Type', 'application/manifest+json');
    }

    /**
     * Sync offline orders (called by service worker)
     */
    public function syncOfflineOrders(Request $request)
    {
        try {
            $offlineOrders = $request->input('orders', []);
            $syncedOrders = [];
            $failedOrders = [];

            foreach ($offlineOrders as $offlineOrder) {
                try {
                    $user = Auth::user();
                    
                    // Prepare order data
                    $data = $offlineOrder['data'];
                    $data['branch_id'] = $user->branch_id;
                    $data['tenant_id'] = $user->tenant_id;
                    $data['cashier_id'] = $user->id;
                    $data['status'] = 'confirmed';
                    $data['offline_order_id'] = $offlineOrder['id'];
                    $data['offline_timestamp'] = $offlineOrder['timestamp'];

                    // Create the order
                    $order = $this->orderService->createOrder($data);

                    $syncedOrders[] = [
                        'offline_id' => $offlineOrder['id'],
                        'order_id' => $order->id,
                        'success' => true
                    ];

                } catch (\Exception $e) {
                    \Log::error('Failed to sync offline order: ' . $e->getMessage(), [
                        'offline_order' => $offlineOrder,
                        'error' => $e->getTraceAsString()
                    ]);

                    $failedOrders[] = [
                        'offline_id' => $offlineOrder['id'],
                        'error' => $e->getMessage(),
                        'success' => false
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'synced_orders' => $syncedOrders,
                'failed_orders' => $failedOrders,
                'total_synced' => count($syncedOrders),
                'total_failed' => count($failedOrders)
            ]);

        } catch (\Exception $e) {
            \Log::error('Offline sync failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get offline data for caching (menu items, customers, etc.)
     */
    public function getOfflineData()
    {
        try {
            $user = Auth::user();
            
            // Get essential data for offline operation
            $customers = Customer::where('tenant_id', $user->tenant_id)
                ->where('is_active', true)
                ->select('id', 'first_name', 'last_name', 'phone', 'email')
                ->orderBy('first_name')
                ->get();

            $tables = Table::where('branch_id', $user->branch_id)
                ->where('is_active', true)
                ->select('id', 'table_number', 'capacity', 'location')
                ->orderBy('table_number')
                ->get();

            $menuItems = MenuItem::whereHas('menu', function($query) use ($user) {
                    $query->where('branch_id', $user->branch_id)
                          ->where('is_active', true);
                })
                ->with(['category:id,name', 'variants:id,menu_item_id,name,price_modifier,is_default', 'addons:id,menu_item_id,name,price,addon_group_name'])
                ->where('is_active', true)
                ->select('id', 'name', 'description', 'base_price', 'category_id', 'image')
                ->orderBy('sort_order')
                ->get();

            $deliveryPersonnel = User::where('tenant_id', $user->tenant_id)
                ->where('is_active', true)
                ->whereHas('roles', function($query) {
                    $query->where('name', 'delivery');
                })
                ->select('id', 'name', 'phone')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'customers' => $customers,
                    'tables' => $tables,
                    'menu_items' => $menuItems,
                    'delivery_personnel' => $deliveryPersonnel,
                    'branch_id' => $user->branch_id,
                    'tenant_id' => $user->tenant_id,
                    'cashier_id' => $user->id,
                    'last_updated' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to get offline data: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get offline data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check connection status and return system info
     */
    public function checkConnection()
    {
        return response()->json([
            'success' => true,
            'online' => true,
            'server_time' => now()->toISOString(),
            'message' => 'Connection is active'
        ]);
    }
}
