<?php $__env->startSection('title', 'إدارة القوائم'); ?>

<?php $__env->startPush('styles'); ?>
<!-- DataTables Tailwind CSS CDN -->
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.tailwindcss.min.css" rel="stylesheet">
<!-- Menu Module Shared Styles -->
<link href="<?php echo e(asset('modules/Menu/resources/assets/css/menu-shared.css')); ?>" rel="stylesheet">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('page-header'); ?>
<div class="bg-white shadow">
    <div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">
        <div class="py-6 md:flex md:items-center md:justify-between lg:border-t lg:border-gray-200">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div>
                        <div class="flex items-center">
                            <h1 class="ml-3 text-2xl font-bold leading-7 text-gray-900 sm:leading-9 sm:truncate">
                                إدارة القوائم
                            </h1>
                        </div>
                        <dl class="mt-6 flex flex-col sm:ml-3 sm:mt-1 sm:flex-row sm:flex-wrap">
                            <dt class="sr-only">الفرع</dt>
                            <dd class="flex items-center text-sm text-gray-500 font-medium capitalize sm:mr-6">
                                <i class="fas fa-building text-gray-400 ml-1.5 h-5 w-5 flex-shrink-0"></i>
                                الفرع الحالي
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Filters Section -->
    <div class="menu-filters-section">
        <div class="menu-filters-header">
            <h3 class="menu-filters-title">فلاتر البحث</h3>
        </div>
        <div class="menu-filters-body">
            <div class="menu-filters-grid">
                <!-- Status Filter -->
                <div class="menu-form-group">
                    <label for="status-filter" class="menu-form-label">الحالة</label>
                    <select id="status-filter" class="menu-form-select">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>

                <!-- Menu Type Filter -->
                <div class="menu-form-group">
                    <label for="menu-type-filter" class="menu-form-label">نوع القائمة</label>
                    <select id="menu-type-filter" class="menu-form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="breakfast">إفطار</option>
                        <option value="lunch">غداء</option>
                        <option value="dinner">عشاء</option>
                        <option value="drinks">مشروبات</option>
                        <option value="desserts">حلويات</option>
                    </select>
                </div>

                <!-- Default Status Filter -->
                <div class="menu-form-group">
                    <label for="default-filter" class="menu-form-label">القائمة الافتراضية</label>
                    <select id="default-filter" class="menu-form-select">
                        <option value="">الكل</option>
                        <option value="yes">افتراضي</option>
                        <option value="no">عادي</option>
                    </select>
                </div>

                <!-- Reset Button -->
                <div class="menu-form-group">
                    <label class="menu-form-label">&nbsp;</label>
                    <button type="button" id="reset-filters" class="menu-btn menu-btn-outline w-full">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- DataTable Section -->
    <div class="menu-card">
        <div class="menu-card-header">
            <div class="flex items-center justify-between">
                <h3 class="menu-filters-title">قائمة القوائم</h3>
                <button type="button" id="add-menu-btn" class="menu-btn menu-btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة قائمة جديدة
                </button>
            </div>
        </div>
        <div class="menu-card-body">
            <div class="overflow-x-auto">
                <table id="menus-table" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكود</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">وقت البداية</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">وقت النهاية</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">افتراضي</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- DataTable will populate this -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Menu Modal -->
<div id="menu-modal" class="menu-modal hidden">
    <div class="menu-modal-content menu-fade-in">
        <div class="menu-modal-header">
            <h3 class="menu-modal-title" id="modal-title">إضافة قائمة جديدة</h3>
            <button type="button" class="menu-modal-close" id="close-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="menu-modal-body">
            <form id="menu-form">
                <input type="hidden" id="menu-id" name="menu_id">

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <!-- Name -->
                    <div class="sm:col-span-2 menu-form-group">
                        <label for="name" class="menu-form-label required">اسم القائمة</label>
                        <input type="text" id="name" name="name" required class="menu-form-input">
                        <div class="menu-form-error hidden" id="name-error"></div>
                    </div>

                    <!-- Code -->
                    <div class="menu-form-group">
                        <label for="code" class="menu-form-label required">كود القائمة</label>
                        <input type="text" id="code" name="code" required class="menu-form-input">
                        <div class="menu-form-error hidden" id="code-error"></div>
                    </div>

                    <!-- Menu Type -->
                    <div class="menu-form-group">
                        <label for="menu_type" class="menu-form-label">نوع القائمة</label>
                        <select id="menu_type" name="menu_type" class="menu-form-select">
                            <option value="">اختر النوع</option>
                            <option value="breakfast">إفطار</option>
                            <option value="lunch">غداء</option>
                            <option value="dinner">عشاء</option>
                            <option value="drinks">مشروبات</option>
                            <option value="desserts">حلويات</option>
                        </select>
                    </div>

                    <!-- Start Time -->
                    <div class="menu-form-group">
                        <label for="start_time" class="menu-form-label">وقت البداية</label>
                        <input type="time" id="start_time" name="start_time" class="menu-form-input time-input">
                    </div>

                    <!-- End Time -->
                    <div class="menu-form-group">
                        <label for="end_time" class="menu-form-label">وقت النهاية</label>
                        <input type="time" id="end_time" name="end_time" class="menu-form-input time-input">
                    </div>

                    <!-- Description -->
                    <div class="sm:col-span-2 menu-form-group">
                        <label for="description" class="menu-form-label">الوصف</label>
                        <textarea id="description" name="description" rows="3" class="menu-form-textarea"></textarea>
                    </div>

                    <!-- Sort Order -->
                    <div class="menu-form-group">
                        <label for="sort_order" class="menu-form-label">ترتيب العرض</label>
                        <input type="number" id="sort_order" name="sort_order" min="0" value="0" class="menu-form-input">
                    </div>

                    <!-- Status Checkboxes -->
                    <div class="menu-form-group space-y-4">
                        <div class="flex items-center">
                            <input id="is_active" name="is_active" type="checkbox" checked class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                            <label for="is_active" class="mr-2 block text-sm text-gray-900">نشط</label>
                        </div>
                        <div class="flex items-center">
                            <input id="is_default" name="is_default" type="checkbox" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                            <label for="is_default" class="mr-2 block text-sm text-gray-900">قائمة افتراضية</label>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="menu-modal-footer">
            <button type="button" id="cancel-btn" class="menu-btn menu-btn-outline">
                إلغاء
            </button>
            <button type="submit" form="menu-form" id="save-btn" class="menu-btn menu-btn-primary">
                <i class="fas fa-save"></i>
                حفظ
            </button>
        </div>
    </div>
</div>

<!-- View Menu Modal -->
<div id="view-menu-modal" class="menu-modal hidden">
    <div class="menu-modal-content menu-fade-in">
        <div class="menu-modal-header">
            <h3 class="menu-modal-title">تفاصيل القائمة</h3>
            <button type="button" class="menu-modal-close" id="close-view-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="menu-modal-body">
            <div id="menu-details" class="space-y-4">
                <!-- Menu details will be populated here -->
            </div>
        </div>

        <div class="menu-modal-footer">
            <button type="button" id="close-view-btn" class="menu-btn menu-btn-outline">
                إغلاق
            </button>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>
<!-- SweetAlert -->
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    let table = $('#menus-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "<?php echo e(route('menus.index')); ?>",
            data: function(d) {
                d.status = $('#status-filter').val();
                d.menu_type = $('#menu-type-filter').val();
                d.is_default = $('#default-filter').val();
            }
        },
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'code', name: 'code'},
            {data: 'menu_type', name: 'menu_type'},
            {data: 'start_time', name: 'start_time'},
            {data: 'end_time', name: 'end_time'},
            {data: 'is_active', name: 'is_active'},
            {data: 'is_default', name: 'is_default'},
            {data: 'created_at', name: 'created_at'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        responsive: true,
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        }
    });

    // Filter functionality
    $('#status-filter, #menu-type-filter, #default-filter').change(function() {
        table.draw();
    });

    // Reset filters
    $('#reset-filters').click(function() {
        $('#status-filter, #menu-type-filter, #default-filter').val('');
        table.draw();
    });

    // Add Menu
    $('#add-menu-btn').click(function() {
        $('#menu-form')[0].reset();
        $('#menu-id').val('');
        $('#modal-title').text('إضافة قائمة جديدة');
        $('#is_active').prop('checked', true);
        $('#is_default').prop('checked', false);
        clearErrors();
        $('#menu-modal').removeClass('hidden');
    });

    // Edit Menu
    $(document).on('click', '.edit-menu', function() {
        let menuId = $(this).data('id');
        
        $.ajax({
            url: `/menu/menus/${menuId}/edit`,
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    let menu = response.data;
                    $('#menu-id').val(menu.id);
                    $('#name').val(menu.name);
                    $('#code').val(menu.code);
                    $('#menu_type').val(menu.menu_type);
                    $('#start_time').val(menu.start_time);
                    $('#end_time').val(menu.end_time);
                    $('#description').val(menu.description);
                    $('#sort_order').val(menu.sort_order || 0);
                    $('#is_active').prop('checked', menu.is_active);
                    $('#is_default').prop('checked', menu.is_default);
                    $('#modal-title').text('تعديل القائمة');
                    clearErrors();
                    $('#menu-modal').removeClass('hidden');
                } else {
                    swal('خطأ!', response.message, 'error');
                }
            },
            error: function(xhr) {
                console.error('Error:', xhr);
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات القائمة', 'error');
            }
        });
    });

    // View Menu
    $(document).on('click', '.show-menu', function() {
        let menuId = $(this).data('id');
        
        $.ajax({
            url: `/menu/menus/${menuId}/show`,
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    let menu = response.data;
                    let detailsHtml = `
                        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">اسم القائمة</label>
                                <p class="mt-1 text-sm text-gray-900">${menu.name}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">الكود</label>
                                <p class="mt-1 text-sm text-gray-900">${menu.code}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">نوع القائمة</label>
                                <p class="mt-1 text-sm text-gray-900">${menu.menu_type || '-'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">ترتيب العرض</label>
                                <p class="mt-1 text-sm text-gray-900">${menu.sort_order || 0}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">وقت البداية</label>
                                <p class="mt-1 text-sm text-gray-900">${menu.start_time || '-'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">وقت النهاية</label>
                                <p class="mt-1 text-sm text-gray-900">${menu.end_time || '-'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">الحالة</label>
                                <p class="mt-1 text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${menu.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                        ${menu.is_active ? 'نشط' : 'غير نشط'}
                                    </span>
                                </p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">افتراضي</label>
                                <p class="mt-1 text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${menu.is_default ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}">
                                        ${menu.is_default ? 'افتراضي' : 'عادي'}
                                    </span>
                                </p>
                            </div>
                            <div class="sm:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">الوصف</label>
                                <p class="mt-1 text-sm text-gray-900">${menu.description || '-'}</p>
                            </div>
                            <div class="sm:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">تاريخ الإنشاء</label>
                                <p class="mt-1 text-sm text-gray-900">${menu.created_at || '-'}</p>
                            </div>
                        </div>
                    `;
                    $('#menu-details').html(detailsHtml);
                    $('#view-menu-modal').removeClass('hidden');
                } else {
                    swal('خطأ!', response.message, 'error');
                }
            },
            error: function(xhr) {
                console.error('Error:', xhr);
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات القائمة', 'error');
            }
        });
    });

    // Delete Menu
    $(document).on('click', '.delete-menu', function() {
        let menuId = $(this).data('id');
        
        swal({
            title: 'هل أنت متأكد؟',
            text: 'لن تتمكن من التراجع عن هذا الإجراء!',
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'إلغاء',
                    value: null,
                    visible: true,
                    className: 'btn btn-secondary',
                    closeModal: true,
                },
                confirm: {
                    text: 'نعم، احذف!',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        }).then((willDelete) => {
            if (willDelete) {
                $.ajax({
                    url: `/menu/menus/${menuId}`,
                    type: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            swal('تم الحذف!', response.message, 'success');
                            table.draw();
                        } else {
                            swal('خطأ!', response.message, 'error');
                        }
                    },
                    error: function(xhr) {
                        console.error('Error:', xhr);
                        swal('خطأ!', 'حدث خطأ أثناء حذف القائمة', 'error');
                    }
                });
            }
        });
    });

    // Save Menu
    $('#menu-form').submit(function(e) {
        e.preventDefault();
        
        let menuId = $('#menu-id').val();
        let url = menuId ? `/menu/menus/${menuId}` : '<?php echo e(route("menus.store")); ?>';
        let method = menuId ? 'PUT' : 'POST';
        
        let formData = {
            name: $('#name').val(),
            code: $('#code').val(),
            menu_type: $('#menu_type').val(),
            start_time: $('#start_time').val(),
            end_time: $('#end_time').val(),
            description: $('#description').val(),
            sort_order: $('#sort_order').val(),
            is_active: $('#is_active').is(':checked') ? 1 : 0,
            is_default: $('#is_default').is(':checked') ? 1 : 0,
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        if (method === 'PUT') {
            formData._method = 'PUT';
        }

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    swal('نجح!', response.message, 'success');
                    $('#menu-modal').addClass('hidden');
                    table.draw();
                } else {
                    swal('خطأ!', response.message, 'error');
                }
            },
            error: function(xhr) {
                console.error('Error:', xhr);
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    displayErrors(errors);
                } else {
                    swal('خطأ!', 'حدث خطأ أثناء حفظ القائمة', 'error');
                }
            }
        });
    });

    // Modal close handlers
    $('#close-modal, #cancel-btn').click(function() {
        $('#menu-modal').addClass('hidden');
    });

    $('#close-view-modal, #close-view-btn').click(function() {
        $('#view-menu-modal').addClass('hidden');
    });

    // Close modal when clicking outside
    $(window).click(function(event) {
        if (event.target.id === 'menu-modal') {
            $('#menu-modal').addClass('hidden');
        }
        if (event.target.id === 'view-menu-modal') {
            $('#view-menu-modal').addClass('hidden');
        }
    });

    // Helper functions
    function displayErrors(errors) {
        clearErrors();

        for (let field in errors) {
            let errorElement = $(`#${field}-error`);
            if (errorElement.length) {
                errorElement.text(errors[field][0]).removeClass('hidden');
                $(`#${field}`).addClass('error');
            }
        }
    }

    function clearErrors() {
        $('.menu-form-error').addClass('hidden');
        $('.menu-form-input, .menu-form-select, .menu-form-textarea').removeClass('error');
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Menu\Providers/../resources/views/menus.blade.php ENDPATH**/ ?>